import React, { useState } from 'react';
import { Calculator, FileSearch, Send, Loader2, ArrowUpRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { createPageUrl } from '@/utils';

export default function Tools() {
  // ROI Calculator State
  const [adSpend, setAdSpend] = useState(5000);
  const [cpc, setCpc] = useState(2.5);
  const [conversionRate, setConversionRate] = useState(3);
  const [saleValue, setSaleValue] = useState(150);
  const [roiResults, setRoiResults] = useState(null);

  // Social Audit State
  const [platform, setPlatform] = useState('');
  const [postFrequency, setPostFrequency] = useState(3);
  const [engagementRate, setEngagementRate] = useState('');
  const [hasBranding, setHasBranding] = useState('');
  const [paidAds, setPaidAds] = useState('');
  const [auditReport, setAuditReport] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [showEmailForm, setShowEmailForm] = useState(false);

  const handleCalculateROI = (e) => {
    e.preventDefault();
    const totalClicks = adSpend / cpc;
    const totalConversions = totalClicks * (conversionRate / 100);
    const totalRevenue = totalConversions * saleValue;
    const profit = totalRevenue - adSpend;
    const roi = (profit / adSpend) * 100;

    setRoiResults({
      totalRevenue: totalRevenue.toFixed(2),
      profit: profit.toFixed(2),
      roi: roi.toFixed(2),
      totalConversions: totalConversions.toFixed(2),
    });
  };

  const handleGenerateAudit = async (e) => {
    e.preventDefault();
    if (!platform || !postFrequency || !engagementRate || !hasBranding || !paidAds) {
      alert("Please fill out all audit fields.");
      return;
    }
    setShowEmailForm(true);
  };
  
  const handleSendAudit = async (e) => {
    e.preventDefault();
    if(!userName || !userEmail) {
        alert("Please enter your name and email.");
        return;
    }
    
    setIsGenerating(true);
    setAuditReport('');

    try {
      // Simulate audit generation
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const mockReport = `Here is your quick audit:

STRENGTHS:
• Your posting frequency of ${postFrequency} times per week is solid
• ${hasBranding === 'Yes' ? 'Great job maintaining consistent branding!' : 'Room for improvement in branding consistency'}
• ${paidAds.includes('Yes') ? 'Smart move running paid ads to amplify reach' : 'Organic reach is good but paid ads could boost results'}

WEAKNESSES:
• Engagement rate of ${engagementRate} could be optimized with better content strategy
• ${platform} algorithm changes require constant adaptation
• Missing conversion tracking and revenue attribution

TOP 3 RECOMMENDATIONS:
1. Implement conversion tracking to measure actual ROI from social media
2. Create more engaging content formats (video, carousels, stories)
3. Set up retargeting campaigns to convert engaged users into customers

Ready to turn these insights into revenue? Book a strategy call with us!`;

      setAuditReport(mockReport);
      console.log(`Audit sent to ${userEmail} for ${userName}`);

    } catch (error) {
      console.error("Error generating audit:", error);
      setAuditReport("Sorry, there was an error generating your report. Please try again later.");
    } finally {
      setIsGenerating(false);
      setShowEmailForm(false);
    }
  };

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-yellow-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-black text-white p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">FREE TOOLS</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-black max-w-3xl mx-auto">
            Calculate your potential ROI and get a free audit of your social media presence.
          </p>
        </div>
      </section>

      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            
            {/* ROI Calculator */}
            <div className="asymmetric-grid">
              <div className="bg-white brutal-border brutal-shadow p-8">
                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-blue-500 text-white w-12 h-12 brutal-border brutal-shadow-small flex items-center justify-center">
                    <Calculator className="w-6 h-6" />
                  </div>
                  <h2 className="brutal-text text-2xl">ROI CALCULATOR</h2>
                </div>
                
                <form onSubmit={handleCalculateROI} className="space-y-6">
                  <div>
                    <label className="brutal-text text-sm mb-2 block">MONTHLY AD SPEND (TSH)</label>
                    <input
                      type="number"
                      value={adSpend}
                      onChange={(e) => setAdSpend(Number(e.target.value))}
                      className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                    />
                  </div>

                  <div>
                    <label className="brutal-text text-sm mb-2 block">COST PER CLICK (TSH)</label>
                    <input
                      type="number"
                      step="0.1"
                      value={cpc}
                      onChange={(e) => setCpc(Number(e.target.value))}
                      className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                    />
                  </div>

                  <div>
                    <label className="brutal-text text-sm mb-2 block">CONVERSION RATE (%)</label>
                    <input
                      type="number"
                      step="0.1"
                      value={conversionRate}
                      onChange={(e) => setConversionRate(Number(e.target.value))}
                      className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                    />
                  </div>

                  <div>
                    <label className="brutal-text text-sm mb-2 block">AVERAGE SALE VALUE (TSH)</label>
                    <input
                      type="number"
                      value={saleValue}
                      onChange={(e) => setSaleValue(Number(e.target.value))}
                      className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                    />
                  </div>
                  
                  <button
                    type="submit"
                    className="w-full bg-blue-500 text-white px-6 py-3 brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150"
                  >
                    CALCULATE ROI
                  </button>
                </form>

                {roiResults && (
                  <div className="mt-8 bg-green-100 brutal-border p-6">
                    <h3 className="brutal-text text-lg mb-4">YOUR RESULTS:</h3>
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div className="bg-white brutal-border p-3">
                        <div className="brutal-text text-2xl text-green-600">TSH {roiResults.totalRevenue}</div>
                        <div className="text-xs font-bold">TOTAL REVENUE</div>
                      </div>
                      <div className="bg-white brutal-border p-3">
                        <div className="brutal-text text-2xl text-green-600">{roiResults.roi}%</div>
                        <div className="text-xs font-bold">ROI</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Social Media Audit */}
            <div className="anti-asymmetric">
              <div className="bg-white brutal-border brutal-shadow p-8">
                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-pink-500 text-white w-12 h-12 brutal-border brutal-shadow-small flex items-center justify-center">
                    <FileSearch className="w-6 h-6" />
                  </div>
                  <h2 className="brutal-text text-2xl">FREE SOCIAL AUDIT</h2>
                </div>

                {!showEmailForm ? (
                  <form onSubmit={handleGenerateAudit} className="space-y-6">
                    <div>
                      <label className="brutal-text text-sm mb-2 block">PRIMARY PLATFORM</label>
                      <select 
                        value={platform} 
                        onChange={(e) => setPlatform(e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        required
                      >
                        <option value="">Select platform</option>
                        <option value="Instagram">Instagram</option>
                        <option value="Facebook">Facebook</option>
                        <option value="LinkedIn">LinkedIn</option>
                        <option value="TikTok">TikTok</option>
                        <option value="Twitter">Twitter</option>
                      </select>
                    </div>

                    <div>
                      <label className="brutal-text text-sm mb-2 block">POSTS PER WEEK: {postFrequency}</label>
                      <input
                        type="range"
                        min="1"
                        max="14"
                        value={postFrequency}
                        onChange={(e) => setPostFrequency(Number(e.target.value))}
                        className="w-full"
                      />
                    </div>

                    <div>
                      <label className="brutal-text text-sm mb-2 block">ENGAGEMENT RATE</label>
                      <select 
                        value={engagementRate} 
                        onChange={(e) => setEngagementRate(e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        required
                      >
                        <option value="">Select engagement rate</option>
                        <option value="Under 1%">Under 1%</option>
                        <option value="1-3%">1-3%</option>
                        <option value="3-6%">3-6%</option>
                        <option value="Over 6%">Over 6%</option>
                      </select>
                    </div>

                    <div>
                      <label className="brutal-text text-sm mb-2 block">CONSISTENT BRANDING</label>
                      <select 
                        value={hasBranding} 
                        onChange={(e) => setHasBranding(e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        required
                      >
                        <option value="">Is branding consistent?</option>
                        <option value="Yes">Yes, very consistent</option>
                        <option value="Somewhat">Somewhat consistent</option>
                        <option value="No">No, not consistent</option>
                      </select>
                    </div>

                    <div>
                      <label className="brutal-text text-sm mb-2 block">RUNNING PAID ADS</label>
                      <select 
                        value={paidAds} 
                        onChange={(e) => setPaidAds(e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        required
                      >
                        <option value="">Are you using paid ads?</option>
                        <option value="Yes, successfully">Yes, successfully</option>
                        <option value="Yes, but struggling">Yes, but struggling</option>
                        <option value="No, but interested">No, but interested</option>
                        <option value="No, organic only">No, organic only</option>
                      </select>
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-pink-500 text-white px-6 py-3 brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150"
                    >
                      GET FREE AUDIT
                    </button>
                  </form>
                ) : (
                  <form onSubmit={handleSendAudit} className="space-y-6">
                    <div>
                      <label className="brutal-text text-sm mb-2 block">YOUR NAME</label>
                      <input
                        type="text"
                        value={userName}
                        onChange={(e) => setUserName(e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="brutal-text text-sm mb-2 block">YOUR EMAIL</label>
                      <input
                        type="email"
                        value={userEmail}
                        onChange={(e) => setUserEmail(e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        required
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={isGenerating}
                      className="w-full bg-green-400 text-black px-6 py-3 brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 disabled:opacity-50 inline-flex items-center justify-center"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          GENERATING AUDIT...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          SEND MY AUDIT
                        </>
                      )}
                    </button>
                  </form>
                )}

                {auditReport && (
                  <div className="mt-8 bg-yellow-100 brutal-border p-6">
                    <h3 className="brutal-text text-lg mb-4">YOUR AUDIT REPORT:</h3>
                    <div className="whitespace-pre-line font-bold text-sm">
                      {auditReport}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-green-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              WANT PROFESSIONAL HELP?
            </h2>
            <p className="text-xl font-bold mb-8">
              These tools give you a taste of what's possible. Ready for a custom strategy that actually generates revenue?
            </p>
            <Link 
              to={createPageUrl("Contact")}
              className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center"
            >
              BOOK YOUR FREE STRATEGY CALL
              <ArrowUpRight className="w-6 h-6 ml-2" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
