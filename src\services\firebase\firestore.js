// Firestore Database Service
import {
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  onSnapshot,
  serverTimestamp,
  increment,
  writeBatch
} from 'firebase/firestore';
import { db, COLLECTIONS } from '../../config/firebase';

/**
 * Generic Firestore service for CRUD operations
 */
export class FirestoreService {
  /**
   * Add a new document to a collection
   * @param {string} collectionName
   * @param {object} data
   * @param {string} createdBy - User ID who created the document
   * @returns {Promise<{success: boolean, id?: string, error?: string}>}
   */
  static async addDocument(collectionName, data, createdBy = null) {
    try {
      // Filter out undefined values to prevent Firestore errors
      const cleanData = Object.fromEntries(
        Object.entries(data).filter(([_, value]) => value !== undefined)
      );

      const docData = {
        ...cleanData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        ...(createdBy && { createdBy })
      };

      const docRef = await addDoc(collection(db, collectionName), docData);

      return {
        success: true,
        id: docRef.id
      };
    } catch (error) {
      console.error(`Error adding document to ${collectionName}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get a document by ID
   * @param {string} collectionName 
   * @param {string} docId 
   * @returns {Promise<{success: boolean, data?: object, error?: string}>}
   */
  static async getDocument(collectionName, docId) {
    try {
      const docRef = doc(db, collectionName, docId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          success: true,
          data: {
            id: docSnap.id,
            ...docSnap.data()
          }
        };
      } else {
        return {
          success: false,
          error: 'Document not found'
        };
      }
    } catch (error) {
      console.error(`Error getting document from ${collectionName}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get all documents from a collection with optional filtering and ordering
   * @param {string} collectionName 
   * @param {object} options - Query options
   * @returns {Promise<{success: boolean, data?: array, error?: string}>}
   */
  static async getDocuments(collectionName, options = {}) {
    try {
      const {
        where: whereClause,
        orderBy: orderByClause,
        limit: limitCount,
        startAfter: startAfterDoc
      } = options;

      let q = collection(db, collectionName);

      // Apply where clause
      if (whereClause) {
        q = query(q, where(whereClause.field, whereClause.operator, whereClause.value));
      }

      // Apply order by
      if (orderByClause) {
        q = query(q, orderBy(orderByClause.field, orderByClause.direction || 'asc'));
      }

      // Apply limit
      if (limitCount) {
        q = query(q, limit(limitCount));
      }

      // Apply pagination
      if (startAfterDoc) {
        q = query(q, startAfter(startAfterDoc));
      }

      const querySnapshot = await getDocs(q);
      const documents = [];
      
      querySnapshot.forEach((doc) => {
        documents.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return {
        success: true,
        data: documents
      };
    } catch (error) {
      console.error(`Error getting documents from ${collectionName}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update a document
   * @param {string} collectionName
   * @param {string} docId
   * @param {object} data
   * @returns {Promise<{success: boolean, error?: string}>}
   */
  static async updateDocument(collectionName, docId, data) {
    try {
      const docRef = doc(db, collectionName, docId);

      // Filter out undefined values to prevent Firestore errors
      const cleanData = Object.fromEntries(
        Object.entries(data).filter(([_, value]) => value !== undefined)
      );

      const updateData = {
        ...cleanData,
        updatedAt: serverTimestamp()
      };

      await updateDoc(docRef, updateData);

      return { success: true };
    } catch (error) {
      console.error(`Error updating document in ${collectionName}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete a document
   * @param {string} collectionName 
   * @param {string} docId 
   * @returns {Promise<{success: boolean, error?: string}>}
   */
  static async deleteDocument(collectionName, docId) {
    try {
      const docRef = doc(db, collectionName, docId);
      await deleteDoc(docRef);
      
      return { success: true };
    } catch (error) {
      console.error(`Error deleting document from ${collectionName}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Listen to real-time updates for a collection
   * @param {string} collectionName 
   * @param {function} callback 
   * @param {object} options - Query options
   * @returns {function} Unsubscribe function
   */
  static subscribeToCollection(collectionName, callback, options = {}) {
    try {
      const {
        where: whereClause,
        orderBy: orderByClause,
        limit: limitCount
      } = options;

      let q = collection(db, collectionName);

      // Apply where clause
      if (whereClause) {
        q = query(q, where(whereClause.field, whereClause.operator, whereClause.value));
      }

      // Apply order by
      if (orderByClause) {
        q = query(q, orderBy(orderByClause.field, orderByClause.direction || 'asc'));
      }

      // Apply limit
      if (limitCount) {
        q = query(q, limit(limitCount));
      }

      return onSnapshot(q, (querySnapshot) => {
        const documents = [];
        querySnapshot.forEach((doc) => {
          documents.push({
            id: doc.id,
            ...doc.data()
          });
        });
        callback(documents);
      }, (error) => {
        console.error(`Error in real-time listener for ${collectionName}:`, error);
        callback(null, error);
      });
    } catch (error) {
      console.error(`Error setting up listener for ${collectionName}:`, error);
      return () => {}; // Return empty unsubscribe function
    }
  }

  /**
   * Listen to real-time updates for a single document
   * @param {string} collectionName 
   * @param {string} docId 
   * @param {function} callback 
   * @returns {function} Unsubscribe function
   */
  static subscribeToDocument(collectionName, docId, callback) {
    try {
      const docRef = doc(db, collectionName, docId);
      
      return onSnapshot(docRef, (docSnap) => {
        if (docSnap.exists()) {
          callback({
            id: docSnap.id,
            ...docSnap.data()
          });
        } else {
          callback(null);
        }
      }, (error) => {
        console.error(`Error in document listener for ${collectionName}/${docId}:`, error);
        callback(null, error);
      });
    } catch (error) {
      console.error(`Error setting up document listener for ${collectionName}/${docId}:`, error);
      return () => {}; // Return empty unsubscribe function
    }
  }

  /**
   * Increment a numeric field in a document
   * @param {string} collectionName 
   * @param {string} docId 
   * @param {string} field 
   * @param {number} value 
   * @returns {Promise<{success: boolean, error?: string}>}
   */
  static async incrementField(collectionName, docId, field, value = 1) {
    try {
      const docRef = doc(db, collectionName, docId);
      await updateDoc(docRef, {
        [field]: increment(value),
        updatedAt: serverTimestamp()
      });
      
      return { success: true };
    } catch (error) {
      console.error(`Error incrementing field ${field} in ${collectionName}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Batch write operations
   * @param {array} operations - Array of {type, collection, id?, data} objects
   * @returns {Promise<{success: boolean, error?: string}>}
   */
  static async batchWrite(operations) {
    try {
      const batch = writeBatch(db);

      operations.forEach(operation => {
        const { type, collection: collectionName, id, data } = operation;
        
        switch (type) {
          case 'add':
            const newDocRef = doc(collection(db, collectionName));
            batch.set(newDocRef, {
              ...data,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp()
            });
            break;
            
          case 'update':
            const updateDocRef = doc(db, collectionName, id);
            batch.update(updateDocRef, {
              ...data,
              updatedAt: serverTimestamp()
            });
            break;
            
          case 'delete':
            const deleteDocRef = doc(db, collectionName, id);
            batch.delete(deleteDocRef);
            break;
            
          default:
            throw new Error(`Unknown batch operation type: ${type}`);
        }
      });

      await batch.commit();
      return { success: true };
    } catch (error) {
      console.error('Error in batch write:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default FirestoreService;
