import React, { useState, useEffect } from 'react';
import { galleryCategories } from '@/data/galleryImages';
import { Card } from '@/components/common';
import { Filter, Eye, ExternalLink } from 'lucide-react';
import { useRealtimeCollection } from '../hooks/useFirestore';
import { COLLECTIONS } from '../services/firebase';

export default function Gallery() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filteredImages, setFilteredImages] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagesLoaded, setImagesLoaded] = useState({});

  // Get gallery images from Firebase
  const { documents: galleryImages, loading, error } = useRealtimeCollection(COLLECTIONS.GALLERY_IMAGES, {
    orderBy: { field: 'createdAt', direction: 'desc' }
  });

  // Filter images by category
  const getImagesByCategory = (category) => {
    if (!galleryImages) return [];
    if (category === 'all') return galleryImages;
    return galleryImages.filter(image => image.category === category);
  };

  useEffect(() => {
    setFilteredImages(getImagesByCategory(selectedCategory));
  }, [selectedCategory, galleryImages]);

  const handleImageLoad = (imageId) => {
    setImagesLoaded(prev => ({ ...prev, [imageId]: true }));
  };

  const openImageModal = (image) => {
    setSelectedImage(image);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
  };

  const getSizeClasses = (size) => {
    switch (size) {
      case 'large':
        return 'col-span-1 md:col-span-2 row-span-2';
      case 'wide':
        return 'col-span-1 md:col-span-2 row-span-1';
      case 'tall':
        return 'col-span-1 row-span-2';
      case 'square':
        return 'col-span-1 row-span-1';
      default:
        return 'col-span-1 row-span-1';
    }
  };

  const getImageHeight = (size) => {
    switch (size) {
      case 'large':
        return 'h-80 md:h-96';
      case 'wide':
        return 'h-48 md:h-64';
      case 'tall':
        return 'h-80 md:h-96';
      case 'square':
        return 'h-64';
      default:
        return 'h-64';
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-500 to-pink-500 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block asymmetric-grid">
            <h1 className="brutal-text text-4xl md:text-6xl mb-4">OUR GALLERY</h1>
            <p className="text-xl font-bold max-w-2xl">
              Explore our portfolio of successful projects across software development, 
              media production, graphic design, and digital marketing.
            </p>
          </div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-12 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <div className="bg-yellow-400 text-black p-4 brutal-border brutal-shadow inline-block anti-asymmetric">
              <h2 className="brutal-text text-2xl flex items-center gap-2">
                <Filter className="w-6 h-6" />
                FILTER PROJECTS
              </h2>
            </div>
          </div>
          
          <div className="flex flex-wrap justify-center gap-4">
            {galleryCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 brutal-border brutal-shadow brutal-text text-sm transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                  selectedCategory === category.id
                    ? `${category.color} ${category.color === 'bg-yellow-400' ? 'text-black' : 'text-white'}`
                    : 'bg-white text-black hover:bg-gray-100'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading && (
            <div className="text-center py-12">
              <div className="bg-white brutal-border brutal-shadow p-8 inline-block">
                <div className="brutal-text text-2xl mb-4">LOADING GALLERY...</div>
                <div className="animate-pulse bg-gray-200 h-4 w-32 mx-auto"></div>
              </div>
            </div>
          )}

          {error && (
            <div className="text-center py-12">
              <div className="bg-red-100 brutal-border brutal-shadow p-8 inline-block">
                <div className="brutal-text text-2xl mb-4 text-red-600">ERROR LOADING GALLERY</div>
                <p className="font-bold text-red-600">{error}</p>
              </div>
            </div>
          )}

          {!loading && !error && filteredImages.length === 0 && (
            <div className="text-center py-12">
              <div className="bg-yellow-100 brutal-border brutal-shadow p-8 inline-block">
                <div className="brutal-text text-2xl mb-4">NO IMAGES FOUND</div>
                <p className="font-bold text-gray-600">
                  {selectedCategory === 'all' ? 'No images have been added yet.' : `No images found in the ${galleryCategories.find(cat => cat.id === selectedCategory)?.name || selectedCategory} category.`}
                </p>
              </div>
            </div>
          )}

          {!loading && !error && filteredImages.length > 0 && (
            <div className="masonry-container columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6">
              {filteredImages.map((image, index) => (
              <div
                key={image.id}
                className={`masonry-item group cursor-pointer mb-6 ${
                  index % 3 === 0 ? 'asymmetric-grid' : index % 3 === 1 ? 'anti-asymmetric' : ''
                }`}
                onClick={() => openImageModal(image)}
              >
                <Card className="overflow-hidden p-0 group-hover:translate-x-2 group-hover:translate-y-2 group-hover:shadow-none transition-all duration-300 flex flex-col">
                  <div className={`relative overflow-hidden ${getImageHeight(image.size)}`}>
                    {/* Loading skeleton */}
                    {!imagesLoaded[image.id] && (
                      <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                        <div className="brutal-text text-gray-400">LOADING...</div>
                      </div>
                    )}

                    <img
                      src={image.imageUrl || image.src}
                      alt={image.alt || image.title}
                      className={`w-full h-full object-cover transition-all duration-300 group-hover:scale-105 ${
                        imagesLoaded[image.id] ? 'opacity-100' : 'opacity-0'
                      }`}
                      onLoad={() => handleImageLoad(image.id)}
                      loading="lazy"
                    />

                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-70 transition-all duration-300 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center">
                        <Eye className="w-8 h-8 text-white mx-auto mb-2" />
                        <p className="brutal-text text-white text-sm">VIEW PROJECT</p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 bg-white">
                    <h3 className="brutal-text text-lg mb-2 leading-tight">{image.title}</h3>
                    <p className="font-bold text-sm text-gray-600 mb-3 leading-relaxed">{image.description}</p>
                    <div className="flex items-center justify-between">
                      <span className={`inline-block px-3 py-1 brutal-text text-xs ${
                        galleryCategories.find(cat => cat.id === image.category)?.color || 'bg-gray-500'
                      } ${image.category === 'design' ? 'text-black' : 'text-white'} brutal-border`}>
                        {image.category.toUpperCase()}
                      </span>
                    </div>
                  </div>
                </Card>
              </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4">
          <div className="bg-white brutal-border brutal-shadow max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="brutal-text text-2xl mb-2">{selectedImage.title}</h3>
                  <p className="font-bold text-gray-600">{selectedImage.description}</p>
                </div>
                <button
                  onClick={closeImageModal}
                  className="bg-red-500 text-white p-2 brutal-border brutal-shadow hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                >
                  ✕
                </button>
              </div>
              
              <div className="mb-4">
                <img
                  src={selectedImage.imageUrl || selectedImage.src}
                  alt={selectedImage.alt || selectedImage.title}
                  className="w-full h-auto brutal-border"
                />
              </div>
              
              <div className="flex justify-between items-center">
                <span className={`px-4 py-2 brutal-text text-sm ${
                  galleryCategories.find(cat => cat.id === selectedImage.category)?.color || 'bg-gray-500'
                } ${selectedImage.category === 'design' ? 'text-black' : 'text-white'} brutal-border`}>
                  {selectedImage.category.toUpperCase()}
                </span>
                
                <button
                  onClick={closeImageModal}
                  className="bg-blue-500 text-white px-6 py-3 brutal-border brutal-shadow brutal-text text-sm hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150 inline-flex items-center gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  CLOSE
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
