import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, Clock, Calendar, User, Tag, Share2, ArrowUpRight } from 'lucide-react';
import { createPageUrl, formatDate } from '@/utils';
import { useRealtimeCollection } from '../hooks/useFirestore';
import { COLLECTIONS } from '../services/firebase';

export default function BlogPost() {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [blogPost, setBlogPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const { documents: allBlogPosts } = useRealtimeCollection(COLLECTIONS.BLOG_POSTS, {
    orderBy: { field: 'publishedAt', direction: 'desc' }
  });

  useEffect(() => {
    if (allBlogPosts && allBlogPosts.length > 0) {
      // Find the blog post by slug or id
      const post = allBlogPosts.find(post => 
        post.slug === slug || post.id === slug
      );
      
      if (post && post.published) {
        setBlogPost(post);
        setError(null);
      } else {
        setError('Blog post not found or not published');
      }
      setLoading(false);
    }
  }, [allBlogPosts, slug]);

  const getCategoryColor = (category) => {
    const colors = {
      'strategy': 'bg-blue-500',
      'tips': 'bg-green-400',
      'case-studies': 'bg-pink-500',
      'industry-news': 'bg-yellow-400',
      'tutorials': 'bg-purple-500'
    };
    return colors[category] || 'bg-gray-500';
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: blogPost.title,
        text: blogPost.excerpt,
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="bg-gray-200 p-8 brutal-border brutal-shadow text-center">
          <h1 className="brutal-text text-2xl mb-4">LOADING...</h1>
          <p className="font-bold text-gray-600">Please wait while we load the blog post.</p>
        </div>
      </div>
    );
  }

  if (error || !blogPost) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="bg-red-500 text-white p-8 brutal-border brutal-shadow text-center max-w-md">
          <h1 className="brutal-text text-2xl mb-4">POST NOT FOUND</h1>
          <p className="font-bold mb-6">{error || 'The blog post you are looking for does not exist.'}</p>
          <Link 
            to={createPageUrl("Blog")}
            className="bg-white text-black px-6 py-3 brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            BACK TO BLOG
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <section className="py-12 bg-gray-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Back Button */}
          <div className="mb-8">
            <Link 
              to={createPageUrl("Blog")}
              className="bg-white text-black px-4 py-2 brutal-border brutal-shadow-small brutal-text text-sm hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150 inline-flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              BACK TO BLOG
            </Link>
          </div>

          {/* Category Badge */}
          <div className={`${getCategoryColor(blogPost.category)} text-white px-4 py-2 brutal-text text-sm inline-block mb-4`}>
            {blogPost.category.toUpperCase()}
          </div>

          {/* Title */}
          <h1 className="brutal-text text-3xl md:text-5xl mb-6 leading-tight">
            {blogPost.title}
          </h1>

          {/* Excerpt */}
          <p className="text-xl font-bold text-gray-700 mb-8 leading-relaxed">
            {blogPost.excerpt}
          </p>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-6 text-sm font-bold text-gray-600 mb-8">
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              <span>{formatDate(blogPost.created_date)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>{blogPost.read_time} min read</span>
            </div>
            <div className="flex items-center gap-2">
              <User className="w-4 h-4" />
              <span>{blogPost.created_by || 'OnGeneral Team'}</span>
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-8">
            {blogPost.tags.map((tag, index) => (
              <span 
                key={index}
                className="bg-white text-gray-700 px-3 py-1 brutal-border brutal-shadow-small text-xs font-bold flex items-center gap-1"
              >
                <Tag className="w-3 h-3" />
                #{tag}
              </span>
            ))}
          </div>

          {/* Share Button */}
          <button
            onClick={handleShare}
            className="bg-pink-500 text-white px-6 py-3 brutal-border brutal-shadow brutal-text text-sm hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center gap-2"
          >
            <Share2 className="w-4 h-4" />
            SHARE POST
          </button>
        </div>
      </section>

      {/* Featured Image */}
      {blogPost.featured_image_url && (
        <section className="py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-gray-200 brutal-border brutal-shadow overflow-hidden">
              <img 
                src={blogPost.featured_image_url} 
                alt={blogPost.title}
                className="w-full h-64 md:h-96 object-cover"
              />
            </div>
          </div>
        </section>
      )}

      {/* Content Section */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white brutal-border brutal-shadow p-8 md:p-12">
            <div className="prose prose-lg max-w-none">
              {/* Content - for now we'll display it as text, but this could be enhanced with markdown rendering */}
              <div className="font-bold text-gray-800 leading-relaxed whitespace-pre-wrap break-words">
                {blogPost.content || 'Content coming soon...'}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-yellow-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-2xl md:text-3xl mb-6">READY TO GROW YOUR BUSINESS?</h2>
            <p className="text-lg font-bold mb-8">
              Let's discuss how our technology solutions can drive your business forward.
            </p>
            <Link 
              to={createPageUrl("Contact")}
              className="bg-black text-white px-8 py-4 brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center gap-2"
            >
              GET STARTED TODAY
              <ArrowUpRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
