import { useState } from 'react';
import { Edit, Trash2, Eye } from 'lucide-react';
import { galleryCategories } from '../../data/galleryImages';

const GalleryGrid = ({ 
  data = [], 
  onEdit, 
  onDelete, 
  onView 
}) => {
  const [imagesLoaded, setImagesLoaded] = useState({});

  const handleImageLoad = (imageId) => {
    setImagesLoaded(prev => ({ ...prev, [imageId]: true }));
  };

  const getImageHeight = (size) => {
    switch (size) {
      case 'large':
        return 'h-80 md:h-96';
      case 'wide':
        return 'h-48 md:h-64';
      case 'tall':
        return 'h-80 md:h-96';
      case 'square':
        return 'h-64';
      default:
        return 'h-64';
    }
  };

  const getCategoryData = (categoryId) => {
    return galleryCategories.find(cat => cat.id === categoryId);
  };

  if (data.length === 0) {
    return (
      <div className="bg-white brutal-border brutal-shadow p-8 text-center">
        <div className="bg-gray-100 brutal-border brutal-shadow p-6">
          <h3 className="brutal-text text-lg mb-2">NO IMAGES FOUND</h3>
          <p className="font-bold text-gray-600">
            No gallery images available. Add some images to get started.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white brutal-border brutal-shadow p-6">
      <div className="masonry-container columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6">
        {data.map((image, index) => {
          const categoryData = getCategoryData(image.category);

          return (
            <div
              key={image.id}
              className={`masonry-item group relative mb-6 ${
                index % 3 === 0 ? 'asymmetric-grid' : index % 3 === 1 ? 'anti-asymmetric' : ''
              }`}
            >
              <div className="bg-white brutal-border brutal-shadow overflow-hidden group-hover:translate-x-2 group-hover:translate-y-2 group-hover:shadow-none transition-all duration-300 flex flex-col">
                {/* Image Container */}
                <div className={`relative overflow-hidden ${getImageHeight(image.size)}`}>
                  {/* Loading skeleton */}
                  {!imagesLoaded[image.id] && (
                    <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                      <div className="brutal-text text-gray-400 text-sm">LOADING...</div>
                    </div>
                  )}
                  
                  <img
                    src={image.imageUrl || image.src}
                    alt={image.alt || image.title}
                    className={`w-full h-full object-cover transition-all duration-300 group-hover:scale-105 ${
                      imagesLoaded[image.id] ? 'opacity-100' : 'opacity-0'
                    }`}
                    onLoad={() => handleImageLoad(image.id)}
                    loading="lazy"
                  />
                  
                  {/* Overlay with actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-70 transition-all duration-300 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
                      {onView && (
                        <button
                          onClick={() => onView(image)}
                          className="p-2 bg-blue-500 text-white brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                          title="View"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      )}
                      {onEdit && (
                        <button
                          onClick={() => onEdit(image)}
                          className="p-2 bg-yellow-400 text-black brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                          title="Edit"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                      )}
                      {onDelete && (
                        <button
                          onClick={() => onDelete(image)}
                          className="p-2 bg-red-500 text-white brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                          title="Delete"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Image Info */}
                <div className="p-4 bg-white">
                  <h3 className="brutal-text text-sm lg:text-base mb-2 leading-tight">{image.title}</h3>
                  <p className="font-bold text-xs text-gray-600 mb-3 leading-relaxed">
                    {image.description || image.alt}
                  </p>

                  <div className="flex items-center justify-between">
                    {categoryData && (
                      <span className={`px-2 py-1 text-xs font-bold brutal-border ${categoryData.color} ${categoryData.id === 'design' ? 'text-black' : 'text-white'}`}>
                        {categoryData.name}
                      </span>
                    )}
                    <span className="text-xs font-bold text-gray-500 uppercase">
                      {image.size}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default GalleryGrid;
