import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Image,
  MessageSquare,
  FileText,
  Download,
  BookOpen,
  Star,
  Menu,
  X,
  Zap,
  LogOut,
  Settings,
  User
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

const AdminLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
    { name: 'Gallery', href: '/admin/gallery', icon: Image },
    { name: 'Contact Messages', href: '/admin/contacts', icon: MessageSquare },
    { name: 'Case Studies', href: '/admin/case-studies', icon: FileText },
    { name: 'Resources', href: '/admin/resources', icon: Download },
    { name: 'Blog Posts', href: '/admin/blog', icon: BookOpen },
    { name: 'Testimonials', href: '/admin/testimonials', icon: Star },
  ];

  const isActive = (href) => {
    if (href === '/admin') {
      return location.pathname === '/admin';
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed top-0 bottom-0 left-0 z-50 w-64 bg-white brutal-border border-r-4 transform ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:z-40 overflow-y-auto flex flex-col`}>
        
        {/* Logo */}
        <div className="flex items-center justify-between h-20 px-6 brutal-border border-b-4 bg-gray-50 flex-shrink-0">
          <Link to="/admin" className="flex items-center group">
            <div className="bg-black text-white px-3 py-2 brutal-border brutal-shadow-small group-hover:translate-x-1 group-hover:translate-y-1 group-hover:shadow-none transition-all duration-150 transform group-hover:rotate-1">
              <div className="flex items-center gap-2">
                <img
                  src="/photos/logo.png"
                  alt="OnGeneral Services Logo"
                  className="w-5 h-5 object-contain"
                  onError={(e) => {
                    // Fallback to icon if logo fails to load
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'block';
                  }}
                />
                <Zap className="w-5 h-5 text-accent-yellow hidden" fill="currentColor" />
                <span className="brutal-text text-sm">ADMIN</span>
              </div>
            </div>
          </Link>
          
          {/* Mobile close button */}
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden brutal-border p-2 brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 mt-8 px-4 overflow-y-auto">
          <ul className="space-y-3 pb-4">
            {navigation.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href);

              return (
                <li key={item.name}>
                  <Link
                    to={item.href}
                    onClick={() => setSidebarOpen(false)}
                    className={`flex items-center gap-3 px-4 py-3 brutal-border brutal-shadow-small transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                      active
                        ? 'bg-primary-blue text-white transform rotate-1'
                        : 'bg-white text-black hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-bold text-sm lg:text-base">{item.name}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Bottom actions */}
        <div className="flex-shrink-0 p-4 space-y-2 border-t-2 border-gray-200">
          {/* User Info */}
          <div className="bg-gray-100 brutal-border p-2 mb-3">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-600" />
              <div className="min-w-0 flex-1">
                <p className="font-bold text-sm text-gray-800 truncate">{user?.name || 'Admin User'}</p>
                <p className="text-xs text-gray-600 truncate">{user?.email || '<EMAIL>'}</p>
              </div>
            </div>
          </div>

          <Link
            to="/admin/settings"
            className="flex items-center gap-3 px-3 py-2 brutal-border brutal-shadow-small bg-white text-black hover:bg-gray-50 transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none"
          >
            <Settings className="w-4 h-4" />
            <span className="font-bold text-sm">Settings</span>
          </Link>

          <button
            onClick={() => {
              logout();
              navigate('/admin/login');
            }}
            className="w-full flex items-center gap-3 px-3 py-2 brutal-border brutal-shadow-small bg-red-500 text-white hover:bg-red-600 transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none"
          >
            <LogOut className="w-4 h-4" />
            <span className="font-bold text-sm">Logout</span>
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar - Fixed positioning */}
        <header className="fixed top-0 right-0 left-0 lg:left-64 bg-white brutal-border border-b-4 h-16 lg:h-20 flex items-center justify-between px-4 lg:px-6 z-30">
          <div className="flex items-center gap-2 lg:gap-4">
            {/* Mobile menu button */}
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden brutal-border p-2 brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
            >
              <Menu className="w-5 h-5" />
            </button>

            <h1 className="brutal-text text-lg sm:text-xl lg:text-2xl">
              <span className="hidden sm:inline">ON-GENERALSERVICES</span>
              <span className="sm:hidden">OGS</span>
              <span className="ml-1">ADMIN</span>
            </h1>
          </div>

          <div className="flex items-center gap-2 lg:gap-4">
            <div className="bg-accent-green text-black px-2 py-1 lg:px-4 lg:py-2 brutal-border brutal-shadow-small transform hover:rotate-1 transition-transform duration-150">
              <div className="flex items-center gap-1 lg:gap-2">
                <User className="w-3 h-3 lg:w-4 lg:h-4" />
                <span className="font-bold text-xs lg:text-sm">{user?.name || 'ADMIN'}</span>
              </div>
            </div>
          </div>
        </header>

        {/* Page content - Add top margin to account for fixed header */}
        <main className="pt-16 lg:pt-20 p-4 lg:p-6 min-h-screen">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
