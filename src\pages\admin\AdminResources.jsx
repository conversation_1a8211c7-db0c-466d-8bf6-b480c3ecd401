import React, { useState } from 'react';
import { Plus, Edit, Trash2, Eye, Download, FileText, Star } from 'lucide-react';
import DataTable from '../../components/admin/DataTable';
import Modal from '../../components/admin/Modal';
import FormField from '../../components/admin/FormField';
import ActionButton from '../../components/admin/ActionButton';
import { useCollection } from '../../hooks/useFirestore';
import { useAuth } from '../../contexts/AuthContext';
import { COLLECTIONS } from '../../services/firebase';

const AdminResources = () => {
  const { user } = useAuth();
  const {
    documents: resourceList,
    loading,
    error,
    addDocument,
    updateDocument,
    deleteDocument
  } = useCollection(COLLECTIONS.RESOURCES, {
    orderBy: { field: 'createdAt', direction: 'desc' }
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingResource, setEditingResource] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: '',
    category: '',
    featured: false,
    email_required: false,
    file_url: '',
    thumbnail_url: ''
  });

  const resourceTypes = [
    { value: 'template', label: 'Template' },
    { value: 'checklist', label: 'Checklist' },
    { value: 'guide', label: 'Guide' },
    { value: 'calculator', label: 'Calculator' },
    { value: 'webinar', label: 'Webinar' },
    { value: 'ebook', label: 'E-book' },
    { value: 'toolkit', label: 'Toolkit' }
  ];

  const resourceCategories = [
    { value: 'strategy', label: 'Strategy' },
    { value: 'advertising', label: 'Advertising' },
    { value: 'analytics', label: 'Analytics' },
    { value: 'content', label: 'Content' },
    { value: 'social-media', label: 'Social Media' },
    { value: 'design', label: 'Design' },
    { value: 'development', label: 'Development' }
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const resourceData = {
        title: formData.title,
        description: formData.description,
        type: formData.type,
        category: formData.category,
        featured: Boolean(formData.featured),
        email_required: Boolean(formData.email_required),
        file_url: formData.file_url || null,
        thumbnail_url: formData.thumbnail_url || null,
        download_count: editingResource ? (editingResource.download_count || 0) : 0
      };

      if (editingResource) {
        // Update existing resource
        const result = await updateDocument(editingResource.id, resourceData);
        if (!result.success) {
          throw new Error(result.error);
        }
      } else {
        // Add new resource
        const result = await addDocument(resourceData, user?.uid);
        if (!result.success) {
          throw new Error(result.error);
        }
      }

      handleCloseModal();
    } catch (error) {
      console.error('Error saving resource:', error);
      alert('Error saving resource: ' + error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (resource) => {
    setEditingResource(resource);
    setFormData({
      title: resource.title || '',
      description: resource.description || '',
      type: resource.type || '',
      category: resource.category || '',
      featured: Boolean(resource.featured),
      email_required: Boolean(resource.email_required),
      file_url: resource.file_url || '',
      thumbnail_url: resource.thumbnail_url || ''
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (resource) => {
    if (window.confirm(`Are you sure you want to delete "${resource.title}"?`)) {
      try {
        const result = await deleteDocument(resource.id);
        if (!result.success) {
          throw new Error(result.error);
        }
      } catch (error) {
        console.error('Error deleting resource:', error);
        alert('Error deleting resource: ' + error.message);
      }
    }
  };

  const handleView = (resource) => {
    if (resource.file_url) {
      window.open(resource.file_url, '_blank');
    } else {
      alert('No file URL available for this resource');
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingResource(null);
    setFormData({
      title: '',
      description: '',
      type: '',
      category: '',
      featured: false,
      email_required: false,
      file_url: '',
      thumbnail_url: ''
    });
  };

  const handleAddNew = () => {
    setEditingResource(null);
    setFormData({
      title: '',
      description: '',
      type: '',
      category: '',
      featured: false,
      email_required: false,
      file_url: '',
      thumbnail_url: ''
    });
    setIsModalOpen(true);
  };

  // Filter resources by category
  const filteredResources = selectedCategory === 'all'
    ? (resourceList || [])
    : (resourceList || []).filter(resource => resource.category === selectedCategory);

  const columns = [
    {
      key: 'title',
      label: 'TITLE',
      sortable: true,
      render: (title, resource) => (
        <div>
          <div className="font-bold">{title}</div>
          <div className="text-sm text-gray-600">{resource.type}</div>
        </div>
      )
    },
    {
      key: 'category',
      label: 'CATEGORY',
      sortable: true,
      render: (category) => {
        const categoryData = resourceCategories.find(cat => cat.value === category);
        return (
          <span className="px-2 py-1 text-xs font-bold bg-blue-100 text-blue-800 brutal-border">
            {categoryData?.label || category.toUpperCase()}
          </span>
        );
      }
    },
    {
      key: 'download_count',
      label: 'DOWNLOADS',
      sortable: true,
      render: (count) => (
        <div className="flex items-center gap-2">
          <Download className="w-4 h-4 text-green-600" />
          <span className="font-bold">{count || 0}</span>
        </div>
      )
    },
    {
      key: 'featured',
      label: 'STATUS',
      render: (featured, resource) => (
        <div className="space-y-1">
          {featured && (
            <span className="px-2 py-1 text-xs font-bold bg-yellow-400 text-black brutal-border">
              FEATURED
            </span>
          )}
          {resource.email_required && (
            <span className="px-2 py-1 text-xs font-bold bg-red-100 text-red-800 brutal-border">
              EMAIL REQUIRED
            </span>
          )}
        </div>
      )
    },
    {
      key: 'createdAt',
      label: 'CREATED',
      sortable: true,
      render: (date) => (
        <span className="text-sm">
          {date ? new Date(date.seconds ? date.seconds * 1000 : date).toLocaleDateString('en-GB') : 'N/A'}
        </span>
      )
    }
  ];

  const getCategoryCounts = () => {
    const counts = { all: (resourceList || []).length };
    resourceCategories.forEach(category => {
      counts[category.value] = (resourceList || []).filter(r => r.category === category.value).length;
    });
    return counts;
  };

  const categoryCounts = getCategoryCounts();
  const totalDownloads = (resourceList || []).reduce((sum, resource) => sum + (resource.download_count || 0), 0);
  const featuredCount = (resourceList || []).filter(r => r.featured).length;

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="brutal-text text-3xl mb-2">RESOURCES MANAGEMENT</div>
          <div className="brutal-text text-gray-400">LOADING...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="brutal-text text-3xl mb-2">RESOURCES MANAGEMENT</div>
          <div className="text-red-500 font-bold">Error: {error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white brutal-border brutal-shadow p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="brutal-text text-3xl mb-2">RESOURCES MANAGEMENT</h1>
            <p className="font-bold text-gray-600">
              Manage downloadable resources, templates, and guides
            </p>
          </div>
          
          <ActionButton 
            onClick={handleAddNew}
            icon={Plus}
            variant="primary"
          >
            ADD NEW RESOURCE
          </ActionButton>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-blue-500 text-white p-3 brutal-border brutal-shadow-small">
              <FileText className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">{(resourceList || []).length}</div>
              <div className="font-bold text-gray-600 text-sm">TOTAL RESOURCES</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-green-500 text-white p-3 brutal-border brutal-shadow-small">
              <Download className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">{totalDownloads}</div>
              <div className="font-bold text-gray-600 text-sm">TOTAL DOWNLOADS</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-yellow-500 text-black p-3 brutal-border brutal-shadow-small">
              <Star className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">{featuredCount}</div>
              <div className="font-bold text-gray-600 text-sm">FEATURED RESOURCES</div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="bg-white brutal-border brutal-shadow p-4">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-4 py-2 brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
              selectedCategory === 'all'
                ? 'bg-black text-white'
                : 'bg-white text-black hover:bg-gray-50'
            }`}
          >
            ALL ({categoryCounts.all})
          </button>
          
          {resourceCategories.map((category) => (
            <button
              key={category.value}
              onClick={() => setSelectedCategory(category.value)}
              className={`px-4 py-2 brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                selectedCategory === category.value
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-black hover:bg-gray-50'
              }`}
            >
              {category.label} ({categoryCounts[category.value] || 0})
            </button>
          ))}
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={filteredResources}
        columns={columns}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        searchable={true}
        filterable={false}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={editingResource ? 'EDIT RESOURCE' : 'ADD NEW RESOURCE'}
        size="large"
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Resource Title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Enter resource title"
              required
            />
            
            <FormField
              label="Type"
              type="select"
              name="type"
              value={formData.type}
              onChange={handleInputChange}
              options={resourceTypes}
              required
            />
            
            <FormField
              label="Category"
              type="select"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              options={resourceCategories}
              required
            />
            
            <div className="space-y-4">
              <FormField
                label="Featured Resource"
                type="checkbox"
                name="featured"
                value={formData.featured}
                onChange={handleInputChange}
              />
              
              <FormField
                label="Require Email for Download"
                type="checkbox"
                name="email_required"
                value={formData.email_required}
                onChange={handleInputChange}
              />
            </div>
          </div>
          
          <FormField
            label="Description"
            type="textarea"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Enter resource description"
            rows={4}
            required
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="File URL"
              name="file_url"
              value={formData.file_url}
              onChange={handleInputChange}
              placeholder="Enter file download URL"
            />
            
            <FormField
              label="Thumbnail URL"
              name="thumbnail_url"
              value={formData.thumbnail_url}
              onChange={handleInputChange}
              placeholder="Enter thumbnail image URL"
            />
          </div>
          
          <div className="flex gap-4 pt-4">
            <ActionButton
              type="submit"
              variant="primary"
              icon={editingResource ? Edit : Plus}
            >
              {editingResource ? 'UPDATE RESOURCE' : 'ADD RESOURCE'}
            </ActionButton>
            
            <ActionButton
              type="button"
              variant="outline"
              onClick={handleCloseModal}
            >
              CANCEL
            </ActionButton>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default AdminResources;
